<template>
  <div>
    <search-form
      ref="searchRef"
      :form-setting="searchFormSetting"
      @search="searchHandle"
      :autoSearch="false"
      label-width="105px"
      @reset="resetHandler"
    ></search-form>
    <div class="table-wrapper">
      <div class="table-header">
        <div class="table-title">数据列表</div>
        <div class="align-r">
          <!-- <button-icon color="plain" @click="gotoPrint">打印</button-icon> -->
          <button-icon color="plain" @click="gotoExport">导出</button-icon>
          <button-icon color="origin" @click="mulRefundHandler('1')">重新扣款</button-icon>
          <button-icon color="origin" @click="mulRefundHandler('2')">原价扣款</button-icon>
          <button-icon color="plain" @click="openPrintSetting">报表设置</button-icon>
        </div>
      </div>
      <div class="table-content">
        <el-table
          v-loading="isLoading"
          :data="tableData"
          ref="tableData"
          stripe
          header-row-class-name="ps-table-header-row"
          @selection-change="handleSelectionChange"
          row-key="id"
        >
          <table-column v-for="(item, index) in currentTableSetting" :key="index" :col="item">
            <template #index="{ row, index }">
              {{ index + 1 + (currentPage - 1) * pageSize }}
            </template>
            <template #face="{ row }">
              <el-button
                type="text"
                class="ps-text"
                size="small"
                @click="clickViewerHandler(row)"
                :disabled="!row.face_url"
              >
                查看
              </el-button>
            </template>
            <template #create_time_upload="{ row }">
              {{ row.create_time }}
            </template>
            <template #operation="{ row }">
              <el-button type="text" size="small" class="ps-text" @click="clickDetail(row)">详情</el-button>
              <template v-if="isOrderActive(row)">
                <el-button
                  v-if="!row.card_info_id && row.order_status === 'ORDER_SUCCESS'"
                  type="text"
                  size="small"
                  class="ps-text"
                  @click="openDrawer(row, 'match')"
                >
                  用户匹配
                </el-button>
                <template >
                  <el-button v-if="row.card_info_id && row.order_status !== 'ORDER_SUCCESS'" type="text" size="small" class="ps-text" @click="clickBtnHandle('origin', row)">
                    原价扣款
                  </el-button>
                  <el-button  v-if="row.card_info_id && row.order_status !== 'ORDER_SUCCESS'"  type="text" size="small" class="ps-text" @click="clickBtnHandle('repay', row)">
                    重新扣款
                  </el-button>
                  <el-button v-if="row.order_status !== 'ORDER_SUCCESS'" type="text" size="small" class="ps-text" @click="openDrawer(row, 'deduction')">
                    扣款
                  </el-button>
                </template>
                <el-button  v-if="row.order_status !== 'ORDER_SUCCESS'" type="text" size="small" class="ps-text" @click="clickBtnHandle('cancel', row)">
                  取消订单
                </el-button>
              </template>
            </template>
          </table-column>
        </el-table>
        <!-- 分页 start -->
        <div class="block ps-pagination" style="text-align: right">
          <pagination
            :onPaginationChange="onPaginationChange"
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            :layout="'total, prev, pager, next,sizes, jumper'"
            :total="totalCount"
          ></pagination>
        </div>
        <!-- 分页 end -->
      </div>
    </div>
    <print-setting
      v-if="dialogPrintVisible"
      :extraParams="{ printType: printType }"
      :tableSetting="tableSetting"
      :defaultCheckedSetting="currentTableSetting"
      :show.sync="dialogPrintVisible"
      @confirm="confirmPrintDialog"
    ></print-setting>
    <!-- 扣款弹窗 -->
    <el-dialog
      width="30%"
      custom-class="el-dialog__body"
      :title="dialogType == 'refund' ? '批量扣款' : '提示'"
      :visible.sync="isShowRefundDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      lock-scroll
      append-to-body
      :show-close="false"
      @close="dialogClose"
    >
      <!--选择扣款模式-->
      <div v-if="dialogType == 'refund'" class="flex-btn-center">
        <div v-if="flagRefundMoney === '1'">确定批量重新扣款吗？</div>
        <div v-if="flagRefundMoney === '2'">确定批量原价扣款吗？</div>
      </div>
      <!--扣款中-->
      <div v-else-if="dialogType == 'refunding'" class="">
        <div>后台执行批量扣款中，请稍后...</div>
        <el-progress :text-inside="true" :stroke-width="24" :percentage="percentage" status="success"></el-progress>
      </div>
      <!--扣款结果-->
      <div v-else>
        <p style="color: black; text-align: center">扣款成功：{{ numberRefundSuccess }}单</p>
        <p style="color: #e0364c; text-align: center">扣款失败：{{ numberRefundFail }}单</p>
      </div>
      <div slot="footer" class="flex-btn-center">
        <el-button
          size="small"
          class="ps-cancel-btn"
          @click="handlerCancleRefund"
          :disabled="isCancleBtnDisable"
          v-loading="dialogCancleLoading"
          v-if="dialogType != 'success'"
        >
          取 消
        </el-button>
        <el-button
          size="small"
          class="ps-btn"
          @click="handlerConfirmRefund"
          v-loading="dialogLoading"
          type="primary"
          :disabled="isConfirmBtnDisable"
          v-if="dialogType == 'refund'"
        >
          确认扣款
        </el-button>
        <el-button
          size="small"
          class="ps-btn"
          @click="handlerConfirmClose"
          v-loading="dialogLoading"
          type="primary"
          :disabled="isConfirmBtnDisable"
          v-if="dialogType == 'success'"
        >
          关闭
        </el-button>
      </div>
    </el-dialog>
    <!-- 异常扣款弹窗 和用户匹配都用这个 -->
    <abnormal-deduction-drawer
      v-if="drawerShow"
      :isshow.sync="drawerShow"
      :type="drawerType"
      :dialogType="drawerType"
      :dialogInfo="drawerFormData"
      :title="drawerTitle"
      :drawerTabs="drawerTabs"
      @confirm="confirmdeDuctionHandleDrawer"
    />
    <!-- 详情 -->
    <ConsumptionDetail
      :isshow.sync="detailDrawerShow"
      :dialogInfo="drawerFormData"
      drawerFormDataType="abnormalOrder"
      :drawerFormDataTabs="0"
      :drawerFormDataTypePath="'consumption'"
    />
    <image-viewer v-model="showViewer" :initial-index="0" :on-close="closeViewer" :preview-src-list="previewSrcList" />
    <!-- 弹窗 end -->
  </div>
</template>

<script>
import { debounce, deepClone } from '@/utils'
import exportExcel from '@/mixins/exportExcel' // 导出混入
import report from '@/mixins/report' // 混入
import { CONSUMPTION_ACCOUNTING_TABLE_SETTING, CONSUMPTION_ACCOUNTING_SEARCH_SETTING } from './constants'
import AbnormalDeductionDrawer from './component/abnormalDeductionDrawer.vue'
import ConsumptionDetail from './ConsumptionDetail'
// import ConsumptionAccountingDetail from './ConsumptionAccountingDetail.vue'
export default {
  mixins: [report, exportExcel],
  props: {},
  data() {
    return {
      searchFormSetting: CONSUMPTION_ACCOUNTING_SEARCH_SETTING,
      isLoading: false,
      tableData: [],
      tableSetting: deepClone(CONSUMPTION_ACCOUNTING_TABLE_SETTING),
      currentTableSetting: [],
      currentPage: 1,
      pageSize: 10,
      totalCount: 0,
      dialogPrintVisible: false,
      detailDrawerShow: false,
      printType: 'ConsumptionAccounting',
      orderIds: [],

      flagRefundMoney: '1', // 重新扣款还是原价扣款
      dialogType: 'refunding', // 弹窗类型
      numberRefundSuccess: 0, // 扣款成功笔数
      numberRefundFail: 0, // 扣款失败笔数
      isShowRefundDialog: false, // 弹窗是否显示
      dialogLoading: false, // 弹窗按钮loading
      timeCount: 3, // 关闭倒计时
      timeThread: null, // 倒计时线程
      isCancleBtnDisable: false,
      isConfirmBtnDisable: false,
      timer: null, // 重复轮训线程
      percentage: 0, // 进度，最高百分白
      dialogCancleLoading: false, // 取消loading
      queryId: null, // 线程ID
      previewSrcList: [],
      showViewer: false,
      drawerShow: false,
      drawerFormData: {},
      drawerType: '',
      drawerTitle: '',
      drawerTabs: '',
      orgKey: []
    }
  },
  watch: {},
  computed: {},
  components: {
    AbnormalDeductionDrawer,
    ConsumptionDetail
  },
  async created() {
    await this.getLevelNameList()
    this.getpayList()
    this.getDeviceList()
    this.setSelectionConfig()
  },
  destroyed() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },
  methods: {
    initLoad() {
      this.getFailureOrderList()
    },
    // 节下流咯
    searchHandle: debounce(function (e) {
      if (e && e === 'search') {
        this.currentPage = 1
        this.initLoad()
      }
    }, 300),
    resetHandler() {},
    // 判断行是否可以被选择
    isRowSelectable(row) {
      // 有卡信息且订单未关闭时才可选择
      return !!row.card_info_id && row.order_status !== 'ORDER_CLOSE' && row.order_status !== 'ORDER_SUCCESS'
    },
    // 设置选择列配置
    setSelectionConfig() {
      // 找到选择列并设置 selectable 属性
      const selectionColumn = this.tableSetting.find(col => col.type === 'selection')
      if (selectionColumn) {
        selectionColumn.selectable = this.isRowSelectable
      }
    },
    // 判断订单是否处于活跃状态（非关闭状态）
    isOrderActive(row) {
      return row.order_status !== 'ORDER_CLOSE'
    },
    // 获取设备列表
    async getDeviceList() {
      this.isLoading = true
      const res = await this.$apis.apiBackgroundDeviceDeviceListPost({
        page: 1,
        page_size: 99999
      })
      this.isLoading = false
      if (res.code === 0) {
        this.searchFormSetting.device_name_list.dataList = res.data.results
      } else {
        this.$message.error(res.msg)
      }
    },

    onPaginationChange(val) {
      this.currentPage = val.current
      this.pageSize = val.pageSize
      this.getFailureOrderList()
    },
    // 格式化查询参数
    formatQueryParams(data) {
      let params = {}
      for (const key in data) {
        if (data[key].value !== '' && data[key].value !== null && data[key].value.length !== 0) {
          if (key === 'order_status_multi') {
            params.order_status_multi = data[key].value.join(',')
          } else if (key !== 'select_time') {
            params[key] = data[key].value
          } else if (data[key].value.length > 0) {
            params.start_time = data[key].value[0] + ' 00:00:00'
            params.end_time = data[key].value[1] + ' 23:59:59'
          }
        }
      }
      return params
    },
    async getFailureOrderList() {
      this.isLoading = true
      let params = this.formatQueryParams(this.searchFormSetting)
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundOrderOrderOfflineListPost({
          ...params,
          abnormal_type: 'ACCOUNTING',
          page: this.currentPage,
          page_size: this.pageSize
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.totalCount = res.data.count
        this.tableData = res.data.results.map(item => {
          let data = deepClone(item)
          let orderPaymen = deepClone(data.order_payment)
          delete data.order_payment
          for (const key in orderPaymen) {
            if (Object.hasOwnProperty.call(orderPaymen, key)) {
              if (this.orgKey.includes(key)) {
                data['org_' + key] = orderPaymen[key]
              } else {
                if (data[key] !== undefined) {
                  data['p_' + key] = orderPaymen[key]
                } else {
                  data[key] = orderPaymen[key]
                }
              }
            }
          }
          return data
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    gotoExport() {
      const option = {
        url: 'apiBackgroundOrderOrderOfflineListExportPost',
        params: {
          ...this.formatQueryParams(this.searchFormSetting),
          abnormal_type: 'ACCOUNTING',
          page: this.currentPage,
          page_size: this.totalCount
        }
      }
      this.exportHandle(option)
    },
    gotoPrint() {
      const params = this.formatQueryParams(this.searchFormSetting)
      const { href } = this.$router.resolve({
        name: 'Print',
        query: {
          print_date_state: true,
          print_type: this.printType,
          print_title: '对账订单',
          result_key: 'results', // 返回的数据处理的data keys
          api: 'apiBackgroundOrderOrderOfflineListPost', // 请求的api
          show_summary: false, // 合计
          show_print_header_and_footer: true, // 打印页头页尾
          table_setting: JSON.stringify(this.tableSetting),
          current_table_setting: JSON.stringify(this.currentTableSetting),
          push_summary: true, // 合计添加到到table数据最后
          isMerge: 0,
          params: JSON.stringify({
            ...params,
            abnormal_type: 'ACCOUNTING',
            page: 1,
            page_size: this.totalCount ? this.totalCount : 10
          })
        }
      })
      window.open(href, '_blank')
    },
    // 获取支付方式 / 支付类型
    async getpayList() {
      const res = await this.$apis.apiBackgroundReportCenterDataReportGetPayInfoPost()
      if (res.code === 0) {
        const result = []
        res.data.result.sub_payways.forEach(d => {
          Object.keys(d).forEach(key => result.push({ label: d[key], value: key }))
        })
        this.searchFormSetting.sub_payway_list.dataList = result
      } else {
        this.$message.error(res.msg)
      }
    },
    // 动态获取组织的层级 添加到表格
    async getLevelNameList() {
      const res = await this.$apis.apiBackgroundGetlevelNameListPost()
      let list = res.data.map(v => {
        this.orgKey.push(v.level)
        return {
          label: v.name,
          key: 'org_' + v.level
        }
      })
      this.tableSetting.splice(6, 0, ...list)
      this.initPrintSetting()
    },
    openDrawer(row, type) {
      this.drawerFormData = row
      this.drawerType = type

      // 设置弹窗标题
      this.drawerTitle = type === 'match' ? '用户匹配' : '扣款'

      // 根据卡信息和人脸信息确定标签页
      this.drawerTabs = this.getDrawerTabs(row)
      this.drawerShow = true
    },
    // 获取抽屉标签页类型
    getDrawerTabs(row) {
      const hasCardInfo = !!row.card_info_id
      const hasFaceUrl = !!row.face_url

      if (!hasCardInfo && hasFaceUrl) {
        return 'faceCheck'
      } else if (!hasCardInfo) {
        return 'searchUser'
      } else {
        return 'scan'
      }
    },
    // 获取确认提示文本
    getConfirmText(type) {
      const textMap = {
        repay: '重新发起扣款吗',
        cancel: '取消订单吗',
        origin: '原价扣款'
      }
      return `确定${textMap[type] || '操作'}？`
    },
    // 处理订单操作
    async handleOrderAction(type, orderId) {
      switch (type) {
        case 'repay':
          await this.repayOrder(orderId)
          break
        case 'cancel':
          await this.closeOrder(orderId)
          break
        case 'origin':
          await this.repayOrder(orderId, true) // 第二个参数为是否原价扣款
          break
        default:
          console.warn('未知的操作类型:', type)
      }
    },
    clickDetail(row) {
      this.drawerFormData = row
      this.detailDrawerShow = true
    },

    clickBtnHandle(type, row) {
      const tipsText = this.getConfirmText(type)
      this.$confirm(tipsText, '提示', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        customClass: 'ps-confirm',
        cancelButtonClass: 'ps-origin-btn',
        confirmButtonClass: 'ps-btn',
        center: true,
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            await this.handleOrderAction(type, row.p_id)
            done()
            instance.confirmButtonLoading = false
          } else {
            if (!instance.confirmButtonLoading) {
              done()
            }
          }
        }
      })
        .then(e => {})
        .catch(e => {})
    },
    // 重新发起订单支付, isOrigin表示是否原价扣款
    async repayOrder(id, isOrigin) {
      if (this.isLoading) {
        return this.$message.error('请勿重复提交！')
      }
      this.isLoading = true
      let params = {
        order_payment_id: id
      }
      if (isOrigin) params.is_original_price = isOrigin
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrderOrderOfflineOrderPayPost(params))
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getFailureOrderList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 重新发起订单支付
    async closeOrder(id) {
      if (this.isLoading) {
        return this.$message.error('请勿重复提交！')
      }
      this.isLoading = true
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundOrderOrderOfflineOrderClosePost({
          order_payment_id: id
        })
      )
      this.isLoading = false
      if (err) {
        this.$message.error(err.message)
        return
      }
      if (res.code === 0) {
        this.$message.success(res.msg)
        this.getFailureOrderList()
      } else {
        this.$message.error(res.msg)
      }
    },
    // 当选择项发生变化时会触发该事件
    handleSelectionChange(val) {
      var selectListId = []
      let data = Object.freeze(val) // 解除下监听吧，节约点资源
      data.map((item, i) => {
        selectListId.push(item.id)
      })
      this.orderIds = deepClone(selectListId)
    },
    // 批量扣款
    mulRefundHandler(type) {
      if (this.dialogLoading) return
      if (!this.orderIds.length) return this.$message.error('请选择要批量扣款的订单！')

      this.dialogType = 'refund'
      this.flagRefundMoney = type
      this.isShowRefundDialog = true
    },

    // 扣款成功
    async handlerConfirmRefund() {
      this.dialogLoading = true
      var params = {
        order_payment_ids: this.orderIds,
        is_original_price: this.flagRefundMoney === '2'
      }
      const [err, res] = await this.$to(this.$apis.apiBackgroundOrderOrderOfflineBulkOrderPayPost(params))
      this.dialogLoading = false
      if (err) {
        this.dialogType = 'refund'
        this.$message.error(err.message || '扣款失败')
        return
      }
      if (res && res.code === 0) {
        var data = res.data || {}
        this.queryId = data.query_id || ''
        this.startQueryHandle()
      } else {
        this.startQueryHandle()
        this.$message.error(res.msg || '扣款失败')
      }
    },
    // 弹窗关闭
    dialogClose() {
      // this.handlerConfirmClose()
      this.handlerCancleRefund()
    },
    // 开始轮询
    startQueryHandle() {
      this.percentage = 0
      this.dialogType = 'refunding'
      this.getResultUrl(this.queryId)
      this.timer = setInterval(() => {
        this.getResultUrl(this.queryId)
      }, 3000)
    },
    // 轮询查看结果
    async getResultUrl(queryId) {
      const [err, res] = await this.$to(
        this.$apis.apiBackgroundOrderOrderOfflineBulkOrderPayTaskQueryPost({
          query_id: queryId
        })
      )
      if (err) {
        this.showErrorMsg(res.msg)
        return
      }
      if (res.code === 0) {
        this.percentage = res.data.progress || 0
        if (res.data && res.data.status === 'success') {
          this.numberRefundSuccess = res.data.success || 0
          this.numberRefundFail = res.data.fail || 0
          clearInterval(this.timer)
          setTimeout(() => {
            this.dialogType = 'success'
          }, 1000)
        } else if (res.data.status === 'failure') {
          this.showErrorMsg(res.msg)
        }
      } else {
        this.showErrorMsg(res.msg)
      }
    },
    // 显示错误信息
    showErrorMsg(msg) {
      this.dialogType = 'refund'
      this.$message.error(msg)
      clearInterval(this.timer)
    },
    // 取消批量扣款
    async handlerCancleRefund() {
      const actions = {
        refunding: async () => {
          clearInterval(this.timer)
          this.timer = null
          this.dialogCancleLoading = true
          const [err, res] = await this.$to(
            this.$apis.apiBackgroundOrderOrderOfflineCancelBulkOrderPayPost({
              query_id: this.queryId
            })
          )
          this.dialogCancleLoading = false
          if (err) {
            this.showErrorMsg(res.msg)
            return
          }
          if (res && res.code === 0) {
            this.numberRefundSuccess = res.data.success || 0
            this.numberRefundFail = res.data.fail || 0
            this.dialogType = 'success'
          } else {
            this.$message.error(res.msg || '取消失败')
          }
        },
        refund: () => {
          this.isShowRefundDialog = false
        },
        default: () => {
          this.handlerConfirmClose()
        }
      }

      const action = actions[this.dialogType] || actions.default
      await action()
    },
    // 关闭弹窗
    handlerConfirmClose() {
      this.isShowRefundDialog = false
      this.dialogType = 'refund'
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      this.getFailureOrderList()
    },
    confirmdeDuctionHandleDrawer(type) {
      if (type === 'drawer') {
        this.getFailureOrderList()
      }
    },
    // 查看预览图
    clickViewerHandler(row) {
      // don't show viewer when preview is false
      // let imgList = row.image_json || []
      // if (imgList) {
      //   imgList = Array.isArray(imgList) ? imgList : JSON.parse(imgList)
      // }
      this.previewSrcList = [row.face_url]
      if (!this.previewSrcList || this.previewSrcList.length === 0) {
        return this.$message.error('暂无图片')
      }
      this.showViewer = true
    },
    // 关闭图片预览
    closeViewer() {
      this.showViewer = false
    }
  }
}
</script>

<style lang="scss" scoped></style>
