import { getDateRang } from '@/utils'
import * as dayjs from 'dayjs'

export const recentSevenDay = [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]

export const DEFAULT_NUTRITION = [
  { name: '千焦耳', key: 'energy_mj', unit: 'kj', type: 'default' },
  { name: '千卡', key: 'energy_kcal', unit: 'Kcal', type: 'default' },
  { name: '碳水化合物', key: 'carbohydrate', unit: 'g', type: 'default' },
  { name: '蛋白质', key: 'protein', unit: 'g', type: 'default' },
  { name: '脂肪', key: 'axunge', unit: 'g', type: 'default' }
]

export const ELEMENT_NUTRITION = [
  { key: 'Ca', name: '钙', unit: 'mg', type: 'element' },
  { key: 'P', name: '磷', unit: 'mg', type: 'element' },
  { key: 'K', name: '钾', unit: 'mg', type: 'element' },
  { key: 'Na', name: '钠', unit: 'mg', type: 'element' },
  { name: '镁', key: 'Mg', unit: 'mg', type: 'element' },
  { key: 'Fe', name: '铁', unit: 'mg', type: 'element' },
  { key: 'I', name: '碘', unit: 'μg', type: 'element' },
  { key: 'Se', name: '硒', unit: 'μg', type: 'element' },
  { key: 'Zn', name: '锌', unit: 'μg', type: 'element' },
  { key: 'Cu', name: '铜', unit: 'μg', type: 'element' },
  { key: 'F', name: '氟', unit: 'μg', type: 'element' },
  { key: 'Cr', name: '铬', unit: 'μg', type: 'element' },
  { key: 'Mo', name: '钼', unit: 'μg', type: 'element' },
  { key: 'Mn', name: '锰', unit: 'μg', type: 'element' }
]

export const VITAMIN_NUTRITION = [
  { key: 'VA', name: '维生素A', unit: 'μg', type: 'vitamin' },
  { key: 'VD', name: '维生素D', unit: 'μg', type: 'vitamin' },
  { key: 'VE', name: '维生素E', unit: 'μg', type: 'vitamin' },
  { key: 'VK', name: '维生素K', unit: 'μg', type: 'vitamin' },
  { key: 'VB1', name: '维生素B1', unit: 'mg', type: 'vitamin' },
  { key: 'VB2', name: '维生素B2', unit: 'mg', type: 'vitamin' },
  { key: 'VB6', name: '维生素B6', unit: 'mg', type: 'vitamin' },
  { key: 'VB12', name: '维生素B12', unit: 'μg', type: 'vitamin' },
  { key: 'VC', name: '维生素C', unit: 'mg', type: 'vitamin' },
  { key: 'VB5', name: '泛酸', unit: 'mg', type: 'vitamin' },
  { key: 'VM', name: '叶酸', unit: 'μg', type: 'vitamin' },
  { key: 'VB3', name: '烟酸', unit: 'μg', type: 'vitamin' },
  { key: 'Choline', name: ' 胆碱', unit: 'mg', type: 'vitamin' },
  { key: 'Nicotinamide', name: '烟酰胺', unit: 'mg', type: 'vitamin' },
  { key: 'VH', name: '生物素', unit: 'mg', type: 'vitamin' }
]

export const NUTRITION_LIST = [...DEFAULT_NUTRITION, ...ELEMENT_NUTRITION, ...VITAMIN_NUTRITION]

// 设备状态
export const DEVICE_STATUS = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '在线',
    value: 'ONLINE'
  },
  {
    label: '离线',
    value: 'OFFLINE'
  }
]

export const PAYMENTSTATE = [
  // { label: '全部', value: '' },
  { label: '待支付', value: 'ORDER_PAYING' },
  { label: '支付成功', value: 'ORDER_SUCCESS' },
  { label: '支付失败', value: 'ORDER_FAILED' },
  { label: '交易冲正中', value: 'ORDER_REVERSALING' },
  { label: '交易冲正', value: 'ORDER_REVERSAL' },
  { label: '退款中', value: 'ORDER_REFUNDING' },
  { label: '已退款', value: 'ORDER_REFUND_SUCCESS' },
  { label: '关闭(用户未支付)', value: 'ORDER_CLOSE' },
  { label: '过期', value: 'ORDER_TIME_OUT' },
  { label: '未知', value: 'ORDER_UNKNOWN' }
]

export const MEALTYPE = [
  { id: 1, label: '全部', value: 'all' },
  { id: 2, label: '早餐', value: 'breakfast' },
  { id: 3, label: '午餐', value: 'lunch' },
  { id: 4, label: '下午茶', value: 'afternoon' },
  { id: 5, label: '晚餐', value: 'dinner' },
  { id: 6, label: '夜宵', value: 'supper' },
  { id: 7, label: '凌晨餐', value: 'morning' }
]

// 提现订单搜索
export const WITHDRAW_ORDER_SEARCH = {
  selectTime: {
    type: 'datetimerange',
    format: 'yyyy-MM-dd HH:mm:ss',
    label: '提现时间',
    clearable: true,
    value: getDateRang(-7)
  },
  orderStatus: {
    type: 'select',
    label: '提现状态',
    value: '',
    placeholder: '请选择',
    dataList: [
      {
        label: '提现中',
        value: 'ORDER_PAYING'
      },
      {
        label: '提现失败',
        value: 'ORDER_FAILED'
      },
      {
        label: '提现成功',
        value: 'ORDER_SUCCESS'
      }
    ]
  },
  tradeNo: {
    type: 'input',
    value: '',
    label: '提现订单号',
    placeholder: '请输入'
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入'
  },
  personNo: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入'
  },
  account_username: {
    type: 'input',
    value: '',
    label: '操作员',
    placeholder: '请输入操作员'
  }
}

// 订单类型
export const PAYMENT_ORDER_TYPE = [
  { label: '预约订单', value: 'reservation' },
  { label: '报餐', value: 'report_meal' },
  { label: '称重', value: 'buffet' },
  { label: '到店就餐', value: 'instore' },
  { label: '其他', value: 'other' }
]

// 申诉状态
export const APPEAL_STATUS = [
  { label: '全部', value: '' },
  { label: '待审核', value: 'wait' },
  { label: '已审核', value: 'success' },
  { label: '已拒绝', value: 'reject' },
  { label: '已撤回', value: 'cancel' }
]

// 处理状态
// export const DEAL_STATUS = [
//   { label: '待处理', value: 'PENDING' },
//   { label: '挂起中', value: 'HANG_OUT' },
//   { label: '已逾期', value: 'TIMEOUT' },
//   { label: '修改金额', value: 'CHANGE_FEE' },
//   { label: '整单不扣费', value: 'ORDER_FREE' },
//   { label: '整单驳回', value: 'ORDER_REJECT' },
//   { label: '退款', value: 'FOOD_REFUND' }
// ]

export const DEAL_STATUS = [
  { label: '全部', value: '' },
  { label: '修改金额', value: 'CHANGE_FEE' },
  { label: '整单不扣费', value: 'ORDER_FREE' },
  { label: '驳回', value: 'ORDER_REJECT' },
  { label: '退款', value: 'ORDER_REFUND' },
  { label: '挂起中', value: 'HANG_OUT' }
]

// 支付类型
export const SUB_PAYWAY = [
  { label: '储值钱包支付', value: 'wallet' },
  { label: '电子钱包支付', value: 'ewallet' },
  { label: '第三方钱包支付', value: 'twallet' },
  { label: '授权代扣支付', value: 'daikou' },
  { label: '数字人民币支付', value: 'ermb' },
  { label: 'JSAPI支付', value: 'jsapi' },
  { label: 'H5支付', value: 'h5' },
  { label: 'WAP支付', value: 'wap' },
  { label: '小程序支付', value: 'miniapp' },
  { label: '现金支付', value: 'cash' },
  { label: 'B扫C支付', value: 'micropay' },
  { label: 'C扫B支付', value: 'scanpay' },
  { label: '刷卡支付', value: 'cardpay' },
  { label: '刷脸支付', value: 'facepay' },
  { label: '会员码支付', value: 'facecode' },
  { label: '缴费方式支付', value: 'jf' },
  { label: '快e付支付', value: 'fastepay' }
]

// 支付方式
export const PAYMENTMETHOD = [
  // { label: '全部', value: '' },
  { label: '朴食储值支付', value: 'PushiPay' },
  { label: '一卡通-鑫澳康支付', value: 'OCOMPAY' },
  { label: '一卡通-石药支付', value: 'SHIYAOPAY' },
  { label: '农行支付', value: 'ABCPay' },
  { label: '建行支付', value: 'CCBPAY' },
  { label: '中行支付', value: 'BOCPAY' },
  { label: '工行支付', value: 'ICBCPAY' },
  { label: '美团支付', value: 'MEITUANPAY' },
  { label: '收钱吧支付', value: 'ShouqianbaPay' },
  { label: '微信支付', value: 'WechatPay' },
  { label: '未知', value: 'UNKNOWN' },
  { label: '现金支付', value: 'CashPay' }
]

const APPEAL_INPUT = {
  trade_no: {
    type: 'input',
    value: '',
    label: '订单号',
    placeholder: '请输入'
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入'
  }
}
const APPEAL_SELECT = {
  payment_order_type: {
    type: 'select',
    label: '订单类型',
    value: '',
    placeholder: '请选择',
    dataList: PAYMENT_ORDER_TYPE
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    collapseTags: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门'
  },
  appeal_status: {
    type: 'select',
    label: '申诉状态',
    value: '',
    placeholder: '请选择',
    dataList: APPEAL_STATUS
  },
  wallet_org: {
    type: 'organizationSelect',
    value: [],
    label: '动账组织',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  }
}
// 待处理订单搜索
export const APPEAL_ORDER_SEARCH_PENDING = {
  time_type: {
    type: 'select',
    label: '',
    value: 'create_time',
    maxWidth: '130px',
    placeholder: '请选择',
    dataList: [
      {
        label: '申诉时间',
        value: 'create_time'
      },
      {
        label: '就餐时间',
        value: 'dining_time'
      }
    ]
  },
  select_time: {
    type: 'datetimerange',
    label: '',
    clearable: true,
    value: getDateRang(-7)
  },
  ...APPEAL_INPUT,
  sub_payway: {
    type: 'select',
    label: '支付类型',
    value: '',
    placeholder: '请选择',
    dataList: SUB_PAYWAY
  },
  payway: {
    type: 'select',
    label: '支付方式',
    value: '',
    placeholder: '请选择',
    dataList: PAYMENTMETHOD
  },
  ...APPEAL_SELECT,
  deal_status: {
    type: 'select',
    label: '处理状态',
    value: '',
    placeholder: '请选择',
    dataList: [
      { label: '全部', value: '' },
      { label: '待处理', value: 'PENDING' },
      { label: '挂起中', value: 'HANG_OUT' }
    ]
  },
  out_trade_no: {
    type: 'input',
    value: '',
    label: '第三方订单号',
    placeholder: '请输入订单号'
  },
  only_rate_fee: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看手续费',
    value: false
  }
}

// 提现订单搜索
export const APPEAL_ORDER_SEARCH_PROCESSED = {
  date_type: {
    type: 'select',
    label: '',
    value: 1,
    maxWidth: '130px',
    placeholder: '请选择',
    dataList: [
      {
        label: '申诉时间',
        value: 1
      },
      {
        label: '就餐时间',
        value: 'dining_time'
      },
      {
        label: '处理时间',
        value: 2
      }
    ]
  },
  select_time: {
    type: 'datetimerange',
    label: '',
    clearable: true,
    value: getDateRang(-7)
  },
  ...APPEAL_INPUT,
  ...APPEAL_SELECT,
  deal_status: {
    type: 'select',
    label: '处理结果',
    value: '',
    placeholder: '请选择',
    dataList: DEAL_STATUS
  },
  account_username: {
    type: 'input',
    value: '',
    label: '操作员',
    placeholder: '请输入操作员'
  },
  out_trade_no: {
    type: 'input',
    value: '',
    label: '第三方订单号',
    placeholder: '请输入订单号'
  },
  only_rate_fee: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看手续费',
    value: false
  }
}

// 脱机扣款失败订单搜索
export const CONSUMPTION_FAILURE = {
  date_type: {
    type: 'select',
    value: 'upload_time',
    maxWidth: '100px',
    dataList: [
      {
        label: '上传时间',
        value: 'upload_time'
      },
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '支付时间',
        value: 'pay_time'
      },
      {
        label: '扣款时间',
        value: 'deduction_time'
      }
    ]
  },
  select_time: {
    type: 'daterange',
    label: '',
    clearable: false,
    value: getDateRang(-7, { format: '{y}-{m}-{d}' })
  },
  consume_organization_ids: {
    labelWidth: '110px',
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  meal_type_list: {
    labelWidth: '110px',
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择',
    dataList: MEALTYPE.slice(1)
  },
  order_status_multi: {
    labelWidth: '110px',
    type: 'select',
    label: '扣款状态',
    multiple: true,
    collapseTags: true,
    placeholder: '请选择',
    filterable: true,
    clearable: true,
    value: ['ORDER_FAILED'],
    dataList: [
      {
        label: '未扣款',
        value: 'ORDER_PAYING,ORDER_FAILED'
      },
      {
        label: '成功',
        value: 'ORDER_SUCCESS'
      },
      {
        label: '失败',
        value: 'ORDER_FAILED'
      },
      {
        label: '取消',
        value: 'ORDER_CLOSE'
      }
    ]
  },
  trade_no: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '总单号/订单号',
    placeholder: '请输入要搜索的订单号'
  },
  device_name_list: {
    labelWidth: '110px',
    type: 'select',
    label: '设备',
    value: '',
    multiple: true,
    collapseTags: true,
    filterable: true,
    listNameKey: 'device_name',
    listValueKey: 'device_name',
    placeholder: '请选择',
    clearable: true,
    dataList: []
  },
  payer_group_ids: {
    labelWidth: '110px',
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    collapseTags: true
  },

  payer_department_group_ids: {
    labelWidth: '110px',
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门'
  },
  name: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '用户姓名',
    placeholder: '请输入要搜索的用户姓名'
  },
  phone: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入手机号'
  },
  person_no: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  operator: {
    labelWidth: '110px',
    type: 'input',
    label: '操作人',
    value: '',
    placeholder: '请输入操作人',
    clearable: true
  }
  // order_status: {
  //   type: 'select',
  //   label: '支付状态',
  //   value: '',
  //   placeholder: '请选择',
  //   dataList: PAYMENTSTATE
  // },
}

export const APPROVE_ORDER = {
  selectTime: {
    type: 'daterange',
    label: '申请时间',
    clearable: false,
    value: getDateRang(-7, { format: '{y}-{m}-{d}' })
  },
  orderType: {
    type: 'select',
    label: '订单类型',
    value: '',
    placeholder: '请选择',
    clearable: true,
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '预约订单',
        value: 'reservation'
      },
      {
        label: '报餐订单',
        value: 'report'
      }
    ]
  },
  payerGroupIds: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    clearable: true,
    collapseTags: true
  },
  payerDepartmentGroupIds: {
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门',
    clearable: true
  },
  organization_id: {
    type: 'organizationSelect',
    value: '',
    label: '动账组织',
    checkStrictly: true,
    isLazy: false,
    clearable: true,
    multiple: false
  },
  // reviewStatus: {
  //   type: 'select',
  //   label: '审核状态',
  //   value: '',
  //   placeholder: '请选择',
  //   dataList: APPEAL_STATUS
  // },
  reviewReason: {
    type: 'select',
    label: '申诉原因',
    value: '',
    placeholder: '请选择',
    clearable: true,
    dataList: [
      { label: '全部', value: '' },
      { label: '食堂原因', value: 'canteen' },
      { label: '个人原因', value: 'self' }
    ]
  }
}

// 消费订单堂食tableSetting
export const CONSUMPTION_SCENE_TABLE = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '总单号', key: 'unified_out_trade_no', width: '150' },
  { label: '订单号', key: 'trade_no', width: '150' },
  { label: '第三方订单号', key: 'out_trade_no', width: '150' },
  { label: '创建时间', key: 'create_time', width: '150', sortable: 'custom' },
  { label: '支付时间', key: 'pay_time', width: '150', sortable: 'custom' },
  { label: '扣款时间', key: 'deduction_time', width: '150', sortable: 'custom' },
  { label: '订单金额', key: 'origin_fee', type: 'money' },
  { label: '优惠金额', key: 'discount_fee', type: 'money' },
  { label: '优惠类型', key: 'discount_type_alias' },
  { label: '餐补', key: 'food_subsidy_fee', type: 'money' },
  { label: '手续费', key: 'rate_fee', type: 'money' },
  // { label: '餐补', key: 'food_subsidy_fee', type: 'money' },
  { label: '实收金额', key: 'pay_fee', type: 'money' },
  { label: '补贴动账', key: 'subsidy_fee', type: 'money' },
  { label: '储值动账', key: 'wallet_fee', type: 'money' },
  { label: '赠送动账', key: 'complimentary_fee', type: 'money' },
  { label: '补贴钱包余额', key: 'subsidy_balance', type: 'money' },
  { label: '储值钱包余额', key: 'wallet_balance', type: 'money' },
  { label: '赠送钱包余额', key: 'complimentary_balance', type: 'money' },
  { label: '支付方式', key: 'sub_payway_alias' },
  { label: '支付类型', key: 'payway_alias' },
  { label: '支付状态', key: 'order_status_alias', type: 'slot', slotName: 'orderStatusAlias' },
  { label: '交易设备', key: 'device_name' },
  // { label: '交易组织', key: 'device_org' },
  { label: '设备状态', key: 'pay_device_status_alias' },
  { label: '制作状态', key: 'making_type_alias' },
  { label: '对账状态', key: 'settle_status_alias' },
  { label: '动账组织', key: 'wallet_org' },
  // { label: '动态组织的', key: 'push' },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '用户姓名', key: 'name' },
  { label: '人员编号', key: 'person_no' },
  { label: '手机号', key: 'phone' },
  { label: '分组', key: 'payer_group_name' },
  { label: '部门', key: 'payer_department_group_name' },
  { label: '备注', key: 'remark' },
  { label: '操作员', key: 'controller' },
  { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right', width: '100' }
]
// 消费订单预约tableSetting
export const CONSUMPTION_RESERVATION_TABLE = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '总单号', key: 'unified_out_trade_no', width: '150' },
  { label: '订单号', key: 'trade_no', width: '150' },
  { label: '第三方订单号', key: 'out_trade_no', width: '150' },
  { label: '创建时间', key: 'create_time', width: '150', sortable: 'custom' },
  { label: '支付时间', key: 'pay_time', width: '150', sortable: 'custom' },
  { label: '预约时间', key: 'reservation_date', width: '150', sortable: 'custom' },
  { label: '报餐时间', key: 'report_date', width: '150', sortable: 'custom' },
  { label: '订单金额', key: 'origin_fee', type: 'money' },
  { label: '优惠金额', key: 'discount_fee', type: 'money' },
  { label: '优惠类型', key: 'discount_type_alias' },
  { label: '券类型', key: 'coupon_type_alias' },
  { label: '抵扣金额', key: 'deduction_fee', type: 'money' },
  { label: '手续费', key: 'rate_fee', type: 'money' },
  { label: '餐补', key: 'food_subsidy_fee', type: 'money' },
  { label: '服务费', key: 'fuwu_fee', type: 'money' },
  { label: '实收金额', key: 'pay_fee', type: 'money' },
  { label: '补贴动账', key: 'subsidy_fee', type: 'money' },
  { label: '储值动账', key: 'wallet_fee', type: 'money' },
  { label: '赠送动账', key: 'complimentary_fee', type: 'money' },
  { label: '补贴钱包余额', key: 'subsidy_balance', type: 'money' },
  { label: '储值钱包余额', key: 'wallet_balance', type: 'money' },
  { label: '赠送钱包余额', key: 'complimentary_balance', type: 'money' },
  { label: '支付方式', key: 'sub_payway_alias' },
  { label: '支付类型', key: 'payway_alias' },
  { label: '支付状态', key: 'order_status_alias' },
  { label: '制作状态', key: 'making_type_alias' },
  { label: '对账状态', key: 'settle_status_alias' },
  { label: '动账组织', key: 'wallet_org' },
  // { label: 'push', key: 'push', hidden: true },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '取餐方式', key: 'take_meal_type_alias' },
  { label: '配送地址', key: 'delivery_addr', width: '150', showTooltip: true },
  { label: '用户姓名', key: 'name' },
  { label: '人员编号', key: 'person_no' },
  { label: '手机号', key: 'phone' },
  { label: '分组', key: 'payer_group_name' },
  { label: '部门', key: 'payer_department_group_name' },
  { label: '备注', key: 'remark' },
  { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right', width: '100' }
]

export const GETMEALTYPE = [
  // { label: '全部', value: '' },
  { label: '堂食', value: 'on_scene' },
  { label: '食堂自提', value: 'bale' },
  { label: '外卖配送', value: 'waimai' },
  { label: '配送柜自取', value: 'cupboard' }
]

export const getRequestParams = (searchFormSetting, page, pageSize) => {
  const searchData = {}
  Object.keys(searchFormSetting).forEach(key => {
    if (
      key !== 'select_time' &&
      key !== 'create_time' &&
      key !== 'payment_time' &&
      key !== 'finish_time' &&
      key !== 'pay_time' &&
      key !== 'third_and_trade_no' &&
      searchFormSetting[key].value !== '' &&
      searchFormSetting[key].value
    ) {
      searchData[key] = searchFormSetting[key].value
    } else if (typeof searchFormSetting[key].value === 'boolean') {
      searchData[key] = searchFormSetting[key].value
    }
  })
  const params = {
    page,
    page_size: pageSize,
    ...searchData
  }

  if (searchFormSetting.select_time?.value?.length === 2) {
    params.start_date = searchFormSetting.select_time.value[0]
    params.end_date = searchFormSetting.select_time.value[1]
  }

  if (searchFormSetting.pay_time?.value?.length === 2) {
    params.start_pay_time = searchFormSetting.pay_time.value[0] + 'T00:00:00'
    params.end_pay_time = searchFormSetting.pay_time.value[1] + 'T23:59:59'
  }

  if (searchFormSetting.create_time?.value?.length === 2) {
    params.start_create_time = searchFormSetting.create_time.value[0] + 'T00:00:00'
    params.end_create_time = searchFormSetting.create_time.value[1] + 'T23:59:59'
  }

  if (searchFormSetting.payment_time?.value?.length === 2) {
    params.start_payment_time = searchFormSetting.payment_time.value[0] + 'T00:00:00'
    params.end_payment_time = searchFormSetting.payment_time.value[1] + 'T23:59:59'
  }

  if (searchFormSetting.finish_time?.value?.length === 2) {
    params.start_finish_time = searchFormSetting.finish_time.value[0] + 'T00:00:00'
    params.end_finish_time = searchFormSetting.finish_time.value[1] + 'T23:59:59'
  }

  if (searchFormSetting.third_and_trade_no?.value) {
    // 消费订单的特殊字段，先这样吧
    params.trade_no = searchFormSetting.third_and_trade_no.value
    params.out_trade_no = searchFormSetting.third_and_trade_no.value
  }
  if (searchFormSetting.pay_time && searchFormSetting.pay_time.value && searchFormSetting.pay_time.value.length === 2) {
    params.start_date = searchFormSetting.pay_time.value[0]
    params.end_date = searchFormSetting.pay_time.value[1]
  }
  if (Reflect.has(searchFormSetting, 'sort_name') && Reflect.has(searchFormSetting, 'sort_type')) {
    params.sort_name = searchFormSetting.sort_name
    params.sort_type = searchFormSetting.sort_type
  }

  return params
}

export const RECENTSEVEN = [dayjs().subtract(7, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
export const RECENTONE = [dayjs().subtract(0, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]

// 退款订单堂食tableSetting
export const REFUND_SCENE_TABLE = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '总单号', key: 'unified_out_trade_no', width: '160' },
  { label: '退款单号', key: 'refund_no', width: '160' },
  { label: '退款时间', key: 'create_time', width: '150' },
  { label: '退款到账时间', key: 'finish_time', width: '150' },
  { label: '原订单号', key: 'origin_trade_no', width: '160' },
  { label: '第三方订单号', key: 'out_trade_no', width: '160' },
  // { label: 'push', key: 'push', hidden: true },
  { label: '原订单设备', key: 'payment_device_name' },
  { label: '原订单金额', key: 'origin_fee', type: 'money' },
  { label: '原订单实收金额', key: 'real_fee', type: 'money' },
  // { label: '原优惠金额', key: 'discount_fee', type: 'money' },
  { label: '手续费', key: 'rate_fee', type: 'money' },
  { label: '服务费', key: 'fuwu_fee', type: 'money' },
  { label: '退款金额', key: 'refund_fee', type: 'money' },
  { label: '餐补返还金额', key: 'food_subsidy_fee', type: 'money' },
  { label: '补贴动账', key: 'subsidy_fee', type: 'money' },
  { label: '储值动账', key: 'wallet_fee', type: 'money' },
  { label: '赠送动账', key: 'complimentary_fee', type: 'money' },
  { label: '动账组织', key: 'wallet_org' },
  // { label: '交易组织', key: 'device_org' },
  { label: '退款类型', key: 'refund_type_alias' },
  { label: '对账状态', key: 'settle_status_alias' },
  { label: '退款渠道', key: 'org_wallet_name' },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '用户姓名', key: 'payer_name' },
  { label: '人员编号', key: 'payer_person_no' },
  { label: '手机号', key: 'payer_phone' },
  { label: '分组', key: 'payer_group' },
  { label: '部门', key: 'payer_department_group' },
  { label: '退款状态', key: 'order_status_alias' },
  { label: '备注', key: 'remark', showTooltip: true },
  { label: '操作员', key: 'account_alias' }
]

// 退款订单预约tableSetting
export const REFUND_RESERVATION_TABLE = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '总单号', key: 'unified_out_trade_no', width: '160' },
  { label: '退款单号', key: 'refund_no', width: '160' },
  { label: '退款时间', key: 'create_time', width: '150' },
  { label: '退款到账时间', key: 'finish_time', width: '150' },
  { label: '就餐时间', key: 'dining_time', width: '150' },
  { label: '原订单号', key: 'origin_trade_no', width: '160' },
  { label: '第三方订单号', key: 'out_trade_no', width: '160' },
  // { label: 'push', key: 'push', hidden: true },
  { label: '原订单金额', key: 'origin_fee', type: 'money' },
  { label: '原订单实收金额', key: 'real_fee', type: 'money' },
  // { label: '原优惠金额', key: 'discount_fee', type: 'money' },
  { label: '手续费', key: 'rate_fee', type: 'money' },
  { label: '服务费', key: 'fuwu_fee', type: 'money' },
  { label: '退款金额', key: 'refund_fee', type: 'money' },
  { label: '餐补返还金额', key: 'food_subsidy_fee', type: 'money' },
  { label: '补贴动账', key: 'subsidy_fee', type: 'money' },
  { label: '储值动账', key: 'wallet_fee', type: 'money' },
  { label: '赠送动账', key: 'complimentary_fee', type: 'money' },
  { label: '动账组织', key: 'wallet_org' },
  { label: '退款类型', key: 'refund_type_alias' },
  { label: '对账状态', key: 'settle_status_alias' },
  { label: '退款渠道', key: 'org_wallet_name' },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '取餐方式', key: 'take_meal_type_alias' },
  { label: '用户姓名', key: 'payer_name' },
  { label: '人员编号', key: 'payer_person_no' },
  { label: '手机号', key: 'payer_phone' },
  { label: '分组', key: 'payer_group' },
  { label: '部门', key: 'payer_department_group' },
  { label: '退款状态', key: 'order_status_alias' },
  { label: '备注', key: 'remark', showTooltip: true },
  { label: '操作员', key: 'account_alias' }
]

// 退款订单堂食tableSetting
export const REFUND_VISITOR_TABLE = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '总单号', key: 'unified_out_trade_no', width: '160' },
  { label: '退款单号', key: 'refund_no', width: '160' },
  { label: '退款时间', key: 'create_time', width: '150' },
  { label: '退款到账时间', key: 'finish_time', width: '150' },
  { label: '原订单号', key: 'origin_trade_no', width: '160' },
  { label: '第三方订单号', key: 'out_trade_no', width: '160' },
  // { label: 'push', key: 'push', hidden: true },
  { label: '原订单金额', key: 'origin_fee', type: 'money' },
  { label: '原订单实收金额', key: 'real_fee', type: 'money' },
  // { label: '原优惠金额', key: 'discount_fee', type: 'money' },
  { label: '手续费', key: 'rate_fee', type: 'money' },
  { label: '服务费', key: 'fuwu_fee', type: 'money' },
  { label: '退款金额', key: 'refund_fee', type: 'money' },
  { label: '餐补返还金额', key: 'food_subsidy_fee', type: 'money' },
  { label: '补贴动账', key: 'subsidy_fee', type: 'money' },
  { label: '储值动账', key: 'wallet_fee', type: 'money' },
  { label: '赠送动账', key: 'complimentary_fee', type: 'money' },
  { label: '动账组织', key: 'wallet_org' },
  // { label: '交易组织', key: 'device_org' },
  { label: '退款类型', key: 'refund_type_alias' },
  { label: '对账状态', key: 'settle_status_alias' },
  { label: '退款渠道', key: 'org_wallet_name' },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '用户姓名', key: 'payer_name' },
  { label: '人员编号', key: 'payer_person_no' },
  { label: '手机号', key: 'payer_phone' },
  { label: '分组', key: 'payer_group' },
  { label: '部门', key: 'payer_department_group' },
  { label: '退款状态', key: 'order_status_alias' },
  { label: '备注', key: 'remark', showTooltip: true },
  { label: '操作员', key: 'account_alias' }
]

export const REFUNDSTATUS = [
  { label: '全部', value: '' },
  { label: '退款中', value: 'ORDER_REFUNDING' },
  { label: '已退款', value: 'ORDER_SUCCESS' }
]

export const REFUNDTYPE = [
  { label: '全部', value: '' },
  { label: '全额退款', value: 'ORDER_ALL_REFUND' },
  { label: '部分退款', value: 'ORDER_PART_REFUND' }
]

// 退款订单堂食searchForm
export const REFUND_SCENE_SEARCH = {
  create_time: {
    type: 'daterange',
    label: '退款时间',
    clearable: false,
    value: RECENTSEVEN
  },
  refund_no: {
    type: 'input',
    value: '',
    label: '退款订单号',
    placeholder: '请输入退款订单号'
  },
  origin_trade_no: {
    type: 'input',
    value: '',
    label: '原订单号',
    placeholder: '请输入原订单号'
  },
  out_trade_no: {
    type: 'input',
    value: '',
    label: '第三方订单号',
    placeholder: '请输入订单号'
  },
  device_name_list: {
    type: 'select',
    label: '原订单设备',
    multiple: true,
    fixedWidth: true,
    checkStrictly: true,
    listNameKey: 'device_name',
    listValueKey: 'device_no',
    value: [],
    placeholder: '请选择',
    clearable: true,
    dataList: [],
    collapseTags: true
  },
  consume_organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    collapseTags: true
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  name: {
    type: 'input',
    value: '',
    label: '用户姓名',
    placeholder: '请输入用户姓名'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入手机号'
  },
  order_status: {
    type: 'select',
    fixedWidth: true,
    label: '退款状态',
    value: '',
    placeholder: '请选择退款状态',
    dataList: REFUNDSTATUS
  },
  refund_type: {
    type: 'select',
    fixedWidth: true,
    label: '退款类型',
    value: '',
    placeholder: '请选择退款类型',
    dataList: REFUNDTYPE
  },
  account_username: {
    type: 'input',
    value: '',
    label: '操作员',
    placeholder: '请输入操作员'
  },
  // only_rate_fee: {
  //   type: 'checkbox',
  //   label: '',
  //   checkboxLabel: '只看手续费',
  //   value: false
  // },
  // only_debt_fee: {
  //   type: 'checkbox',
  //   label: '',
  //   checkboxLabel: '只看透支',
  //   value: false
  // },
  only_debt_rate_list: {
    type: 'select',
    fixedWidth: true,
    label: '筛选偏好',
    value: [''],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择筛选偏好',
    dataList: [
      { label: '全部', value: '' },
      { label: '仅手续费订单', value: 'only_rate_fee' },
      { label: '仅透支订单', value: 'only_debt_fee' }
    ]
  }
  // device_org: {
  //   type: 'organizationParentSelect',
  //   value: [],
  //   label: '交易组织',
  //   checkStrictly: true,
  //   isLazy: false,
  //   multiple: true
  // }
}

// 退款订单预约searchForm
export const REFUND_RESERVATION_SEARCH = {
  time_type: {
    type: 'select',
    label: '',
    value: 'create_time',
    maxWidth: '100px',
    placeholder: '请选择',
    dataList: [
      {
        label: '退款时间',
        value: 'create_time'
      },
      {
        label: '就餐时间',
        value: 'dining_time'
      }
    ]
  },
  create_time: {
    type: 'daterange',
    label: '',
    clearable: false,
    value: RECENTSEVEN
  },
  refund_no: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '退款订单号',
    placeholder: '请输入退款订单号'
  },
  origin_trade_no: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '原订单号',
    placeholder: '请输入原订单号'
  },
  out_trade_no: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '第三方订单号',
    placeholder: '请输入订单号'
  },
  consume_organization_ids: {
    labelWidth: '110px',
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  payer_department_group_ids: {
    labelWidth: '110px',
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门'
  },
  payer_group_ids: {
    labelWidth: '110px',
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    collapseTags: true
  },
  person_no: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  name: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '用户姓名',
    placeholder: '请输入用户姓名'
  },
  phone: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入手机号'
  },
  take_meal_type: {
    labelWidth: '110px',
    type: 'select',
    fixedWidth: true,
    label: '取餐方式',
    value: '',
    placeholder: '请选择取餐方式',
    dataList: GETMEALTYPE
  },
  order_status: {
    labelWidth: '110px',
    type: 'select',
    fixedWidth: true,
    label: '退款状态',
    value: '',
    placeholder: '请选择退款状态',
    dataList: REFUNDSTATUS
  },
  refund_type: {
    labelWidth: '110px',
    type: 'select',
    fixedWidth: true,
    label: '退款类型',
    value: '',
    placeholder: '请选择退款类型',
    dataList: REFUNDTYPE
  },
  account_username: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '操作员',
    placeholder: '请输入操作员'
  },
  // only_rate_fee: {
  //   labelWidth: '110px',
  //   type: 'checkbox',
  //   label: '',
  //   checkboxLabel: '只看手续费',
  //   value: false
  // },
  // only_debt_fee: {
  //   labelWidth: '110px',
  //   type: 'checkbox',
  //   label: '',
  //   checkboxLabel: '只看透支',
  //   value: false
  // },
  only_debt_rate_list: {
    type: 'select',
    fixedWidth: true,
    label: '筛选偏好',
    value: [''],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择筛选偏好',
    dataList: [
      { label: '全部', value: '' },
      { label: '仅手续费订单', value: 'only_rate_fee' },
      { label: '仅透支订单', value: 'only_debt_fee' }
    ]
  }
}

// 退款订单堂食searchForm
export const REFUND_VISITOR_SEARCH = {
  create_time: {
    type: 'daterange',
    label: '退款时间',
    clearable: false,
    value: RECENTSEVEN
  },
  refund_no: {
    type: 'input',
    value: '',
    label: '退款订单号',
    placeholder: '请输入退款订单号'
  },
  origin_trade_no: {
    type: 'input',
    value: '',
    label: '原订单号',
    placeholder: '请输入原订单号'
  },
  out_trade_no: {
    type: 'input',
    value: '',
    label: '第三方订单号',
    placeholder: '请输入订单号'
  },
  consume_organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    collapseTags: true
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  name: {
    type: 'input',
    value: '',
    label: '用户姓名',
    placeholder: '请输入用户姓名'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入手机号'
  },
  order_status: {
    type: 'select',
    fixedWidth: true,
    label: '退款状态',
    value: '',
    placeholder: '请选择退款状态',
    dataList: REFUNDSTATUS
  },
  refund_type: {
    type: 'select',
    fixedWidth: true,
    label: '退款类型',
    value: '',
    placeholder: '请选择退款类型',
    dataList: REFUNDTYPE
  },
  account_username: {
    type: 'input',
    value: '',
    label: '操作员',
    placeholder: '请输入操作员'
  },
  only_debt_rate_list: {
    type: 'select',
    fixedWidth: true,
    label: '筛选偏好',
    value: [''],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择筛选偏好',
    dataList: [
      { label: '全部', value: '' },
      { label: '仅手续费订单', value: 'only_rate_fee' },
      { label: '仅透支订单', value: 'only_debt_fee' }
    ]
  }
}

// 充值订单tableSetting
export const RECHARGE_TABLE = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '充值订单号', key: 'trade_no', width: '160' },
  { label: '充值时间', key: 'create_time', width: '160' },
  { label: '充值到账时间', key: 'finish_time', width: '150' },
  { label: '充值金额', key: 'pay_fee', type: 'money' },
  { label: '手续费', key: 'rate_fee', type: 'money' },
  { label: '储值钱包到账', key: 'wallet_fee', type: 'money' },
  { label: '赠送钱包到账', key: 'complimentary_fee', type: 'money' },
  { label: '动账钱包', key: 'wallet_name' },
  { label: '姓名', key: 'name' },
  { label: '人员编号', key: 'person_no' },
  { label: '手机号码', key: 'phone' },
  { label: '充值类型', key: 'pay_scene_alias' },
  { label: '充值渠道', key: 'payway_alias' },
  { label: '第三方订单号', key: 'out_trade_no', width: '160' },
  { label: '交易流水号', key: 'provider_trade_no', width: '160' },
  { label: '充值状态', key: 'order_status_alias' },
  { label: '对账状态', key: 'settle_status_alias' },
  { label: '退款状态', key: 'refund_type_alias' },
  { label: '备注', key: 'remark' },
  { label: '操作员', key: 'account_alias' },
  { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right', width: '100' }
]

// 充值退款订单tableSetting
export const RECHARGE_REFUND_TABLE = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '充值退款订单号', key: 'refund_no', width: '160' },
  { label: '退款时间', key: 'create_time', width: '160' },
  { label: '退款金额', key: 'refund_fee', type: 'money' },
  { label: '手续费', key: 'rate_fee', type: 'money' },
  { label: '原充值金额', key: 'origin_fee', type: 'money' },
  { label: '储值钱包到账', key: 'wallet_fee', type: 'money' },
  { label: '动账钱包', key: 'wallet_name' },
  { label: '原赠送钱包到账', key: 'complimentary_fee', type: 'money' },
  { label: '赠送钱包退款金额', key: 'complimentary_fee_2', type: 'money' },
  { label: '姓名', key: 'name' },
  { label: '人员编号', key: 'person_no' },
  { label: '手机号码', key: 'phone' },
  { label: '退款类型', key: 'pay_scene_alias' },
  { label: '退款方式', key: 'refund_type_alias' },
  { label: '退款渠道', key: 'payway_alias' },
  { label: '第三方订单号', key: 'out_trade_no', width: '160' },
  { label: '交易流水号', key: 'provider_trade_no', width: '160' },
  { label: '对账状态', key: 'settle_status_alias' },
  { label: '退款状态', key: 'order_status_alias' },
  { label: '备注', key: 'remark', showTooltip: true },
  { label: '操作员', key: 'account_alias' }
]

export const RECHARGE_TYPEARR = [
  { label: '全部', value: '' },
  { label: '线上', value: 'charge' },
  { label: '线下', value: 'charge_offline' }
]

export const RECHARGE_STATEARR = [
  { label: '全部', value: '' },
  { label: '充值中', value: 'ORDER_REVERSALING' },
  { label: '充值失败', value: 'ORDER_FAILED' },
  { label: '充值成功', value: 'ORDER_SUCCESS' }
]

export const REFUND_STATEARR = [
  { label: '全部', value: '' },
  { label: '退款中', value: 'ORDER_REFUNDING' },
  { label: '退款失败', value: 'ORDER_FAILED' },
  { label: '退款成功', value: 'ORDER_SUCCESS' }
]

export const APPEAL_PENDING_TABLE = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '订单号', key: 'trade_no', width: '160' },
  { label: '第三方订单号', key: 'out_trade_no', width: '160' },
  { label: '申诉时间', key: 'create_time', width: '160' },
  { label: '就餐时间', key: 'dining_time', width: '160' },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '剩余处理时间', key: 'residual_time', width: '160', type: 'slot', slotName: 'residual' },
  { label: '订单金额', key: 'origin_fee' },
  { label: '实收金额', key: 'pay_fee' },
  { label: '手续费', key: 'watting_rate_fee' },
  { label: '补贴动账', key: 'subsidy_fee' },
  { label: '储值动账', key: 'wallet_fee' },
  { label: '赠送动账', key: 'complimentary_fee' },
  { label: '补贴钱包余额', key: 'subsidy_balance' },
  { label: '储值钱包余额', key: 'wallet_balance' },
  { label: '赠送钱包余额', key: 'complimentary_balance' },
  { label: '动账组织', key: 'wallet_org' },
  { label: '订单类型', key: 'payment_order_type_alias' },
  { label: '支付类型', key: 'payway_alias' },
  { label: '支付方式', key: 'sub_payway_alias' },
  { label: '支付状态', key: 'order_status_alias' },
  { label: '对账状态', key: 'settle_status_alias' },
  { label: '申诉状态', key: 'appeal_status_alias' },
  { label: '处理状态', key: 'deal_status_alias' },
  { label: '申诉原因', key: 'appeal_reason' },
  // { label: '组织', key: 'push' },
  { label: '姓名', key: 'name' },
  { label: '人员编号', key: 'person_no' },
  { label: '手机号', key: 'phone' },
  { label: '分组', key: 'payer_group_name' },
  { label: '部门', key: 'payer_department_group_name' },
  { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right', width: '100' }
]

export const APPEAL_PROCESSED_TABLE = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '订单号', key: 'trade_no', width: '160' },
  { label: '第三方订单号', key: 'out_trade_no', width: '160' },
  { label: '申诉时间', key: 'create_time', width: '160' },
  { label: '就餐时间', key: 'dining_time', width: '160' },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '处理时间', key: 'finish_time', width: '160' },
  { label: '退款单号', key: 'refund_no', width: '160' },
  { label: '退款时间', key: 'finish_time', width: '160' },
  { label: '订单金额', key: 'origin_fee' },
  { label: '实收金额', key: 'pay_fee' },
  { label: '手续费', key: 'deal_rate_fee' },
  { label: '退款金额', key: 'refund_fee' },
  { label: '补贴动账', key: 'subsidy_fee' },
  { label: '储值动账', key: 'wallet_fee' },
  { label: '赠送动账', key: 'complimentary_fee' },
  { label: '补贴钱包余额', key: 'subsidy_balance' },
  { label: '储值钱包余额', key: 'wallet_balance' },
  { label: '赠送钱包余额', key: 'complimentary_balance' },
  { label: '动账组织', key: 'wallet_org' },
  { label: '订单类型', key: 'payment_order_type_alias' },
  { label: '支付类型', key: 'payway_alias' },
  { label: '支付方式', key: 'sub_payway_alias' },
  { label: '支付状态', key: 'order_status_alias' },
  { label: '对账状态', key: 'settle_status_alias' },
  { label: '申诉状态', key: 'appeal_status_alias' },
  { label: '处理结果', key: 'deal_status_alias' },
  { label: '申诉原因', key: 'appeal_reason' },
  // { label: '组织', key: 'push' },
  { label: '姓名', key: 'name' },
  { label: '人员编号', key: 'person_no' },
  { label: '手机号', key: 'phone' },
  { label: '分组', key: 'payer_group_name' },
  { label: '部门', key: 'payer_department_group_name' },
  { label: '操作员', key: 'account_alias' },
  { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right', width: '100' }
]

// 售货机订单 消费订单tableSetting
export const CONSUMPTION_SCENE_AUTO_TABLE = [
  { label: '序号', key: 'index', width: '80', type: 'slot', slotName: 'index' },
  { label: '总单号', key: 'unified_out_trade_no', width: '150' },
  { label: '订单号', key: 'trade_no', width: '150' },
  { label: '创建时间', key: 'create_time', width: '150' },
  { label: '支付时间', key: 'pay_time', width: '150' },
  { label: '扣款时间', key: 'deduction_time', width: '150' },
  { label: '储值动账', key: 'wallet_fee', type: 'money' },
  { label: '补贴动账', key: 'subsidy_fee', type: 'money' },
  { label: '赠送动账', key: 'complimentary_fee', type: 'money' },
  { label: '订单金额', key: 'origin_fee', type: 'money' },
  { label: '优惠金额', key: 'discount_fee', type: 'money' },
  { label: '优惠类型', key: 'discount_type_alias' },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '餐补', key: 'food_subsidy_fee', type: 'money' },
  { label: '手续费', key: 'rate_fee', type: 'money' },
  { label: '实收金额', key: 'pay_fee', type: 'money' },
  { label: '储值钱包余额', key: 'wallet_balance', type: 'money' },
  { label: '补贴钱包余额', key: 'subsidy_balance', type: 'money' },
  { label: '赠送钱包余额', key: 'complimentary_balance', type: 'money' },
  { label: '支付方式', key: 'sub_payway_alias' },
  { label: '支付类型', key: 'payway_alias' },
  { label: '支付状态', key: 'order_status_alias' },
  { label: '交易设备', key: 'device_name' },
  { label: '一级组织', key: 'primary' },
  { label: '二级组织', key: 'secondary' },
  { label: '动账组织', key: 'wallet_org' },
  { label: '用户姓名', key: 'name' },
  { label: '人员编号', key: 'person_no' },
  { label: '手机号', key: 'phone' },
  { label: '分组', key: 'payer_group_name' },
  { label: '部门', key: 'payer_department_group_name' },
  { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right', width: '100' }
]

// 售货机订单 退款订单tableSetting
export const REFUND_ORDER_AUTO_TABLE = [
  { label: '序号', key: 'index', width: '80', type: 'slot', slotName: 'index' },
  { label: '退款单号', key: 'refund_no', width: '160' },
  { label: '总单号', key: 'unified_out_trade_no', width: '160' },
  { label: '退款时间', key: 'finish_time', width: '160' },
  { label: '原订单号', key: 'origin_trade_no', width: '160' },
  { label: '第三方订单号', key: 'out_trade_no', width: '160' },
  { label: '原订单金额', key: 'origin_fee', type: 'money' },
  { label: '原订单应收金额', key: 'real_fee', type: 'money' },
  { label: '手续费', key: 'deal_rate_fee', type: 'money' },
  { label: '退款金额', key: 'refund_fee', type: 'money' },
  { label: '储值动账', key: 'wallet_fee', type: 'money' },
  { label: '补贴动账', key: 'subsidy_fee', type: 'money' },
  { label: '赠送动账', key: 'complimentary_fee', type: 'money' },
  { label: '退款类型', key: 'refund_type_alias' },
  { label: '对账状态', key: 'settle_status_alias' },
  { label: '退款渠道', key: 'payway_alias' },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '退款状态', key: 'order_status_alias' },
  { label: '一级组织', key: 'primary' },
  { label: '二级组织', key: 'secondary' },
  { label: '动账组织', key: 'wallet_org' },
  { label: '用户姓名', key: 'payer_name' },
  { label: '人员编号', key: 'payer_person_no' },
  { label: '手机号码', key: 'payer_phone' },
  { label: '分组', key: 'payer_group' },
  { label: '部门', key: 'payer_department_group' },
  { label: '操作员', key: 'account_alias' }
]
// 售货机订单 销售统计tableSetting
export const COLLECT_ORDER_AUTO_TABLE = [
  { label: '序号', key: 'index', width: '80', type: 'slot', slotName: 'index' },
  { label: '商品名称', key: 'name', width: '160' },
  { label: '单价', key: 'raw_fee', width: '160', type: 'money' },
  // { label: '进价', key: 'origin_fee', width: "160", type: 'money' },
  { label: '销售数量', key: 'sale_count', width: '160', sortable: 'custom' },
  { label: '销售额', key: 'sale_money', width: '160', type: 'money', sortable: 'custom' },
  { label: '退款数量', key: 'refund_count', width: '160' },
  { label: '退款金额', key: 'refund_money', width: '160', type: 'money' },
  { label: '实际销售数量', key: 'real_sale_count', width: '160' },
  { label: '实收金额', key: 'real_sale_money', width: '160', type: 'money' }
  // { label: '实际利润', key: 'real_sale_profit', width: "160", type: 'money', sortable: 'custom' }
]
// 退款类型
export const REFUND_TYPE = [
  { label: '全部', value: '' },
  { label: '部分退款', value: 'ORDER_PART_REFUND' },
  { label: '全额退款', value: 'ORDER_ALL_REFUND' }
]

// 充值订单的退款类型
export const REFUND_TYPE_LIST = [
  { label: '部分退款', value: 'ORDER_PART_REFUND' },
  { label: '全额退款', value: 'ORDER_ALL_REFUND' },
  { label: '未退款', value: 'NO_REFUND' }
]
// 消费订单售货柜筛选设置
export const CONSUMPTION_SCENE_AUTO_SEARCH = {
  date_type: {
    type: 'select',
    value: 'create_time',
    maxWidth: '130px',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '支付时间',
        value: 'pay_time'
      },
      {
        label: '扣款时间',
        value: 'deduction_time'
      }
    ]
  },
  select_time: {
    type: 'daterange',
    label: '',
    clearable: false,
    value: RECENTSEVEN
  },
  payway_list: {
    type: 'select',
    label: '支付类型',
    multiple: true,
    collapseTags: true,
    value: [],
    placeholder: '请选择',
    dataList: []
  },
  sub_payway_list: {
    type: 'select',
    label: '支付方式',
    multiple: true,
    collapseTags: true,
    value: [],
    placeholder: '请选择',
    dataList: []
  },
  consume_organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    checkStrictly: true,
    isLazy: false,
    multiple: true,
    collapseTags: true
  },
  wallet_org: {
    type: 'organizationSelect',
    value: [],
    label: '动账组织',
    checkStrictly: true,
    isLazy: false,
    multiple: true,
    collapseTags: true
  },
  device_name_list: {
    type: 'select',
    label: '交易设备',
    multiple: true,
    checkStrictly: true,
    value: [],
    listNameKey: 'name',
    listValueKey: 'key',
    placeholder: '请选择',
    clearable: true,
    dataList: [],
    collapseTags: true
  },
  meal_type_list: {
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择',
    dataList: MEALTYPE.slice(1)
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    collapseTags: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门'
  },
  name: {
    type: 'input',
    value: '',
    label: '用户姓名',
    placeholder: '请输入'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入'
  },
  trade_no: {
    type: 'input',
    value: '',
    labelWidth: '145px',
    label: '总单号/订单号',
    placeholder: '请输入'
  },
  food_status: {
    type: 'select',
    value: '',
    labelWidth: '145px',
    label: '出货状态',
    placeholder: '请选择',
    dataList: [
      {
        label: '全部',
        value: ''
      },
      {
        label: '已出货',
        value: 'success'
      },
      {
        label: '未出货',
        value: 'fail'
      }
    ]
  },
  order_status: {
    type: 'select',
    label: '支付状态',
    value: '',
    placeholder: '请选择',
    dataList: PAYMENTSTATE
  },
  only_discount: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看优惠',
    value: false
  },
  only_rate_fee: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看手续费',
    value: false
  },
  only_debt_fee: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看透支',
    value: false
  }
}
// 退款订单售货柜筛选设置
export const REFUND_ORDER_AUTO_SEARCH = {
  create_time: {
    type: 'daterange',
    label: '退款时间',
    clearable: false,
    value: RECENTSEVEN,
    format: 'yyyy-MM-dd'
  },
  refund_no: {
    type: 'input',
    value: '',
    labelWidth: '145px',
    label: '退款单号',
    placeholder: '请输入'
  },
  origin_trade_no: {
    type: 'input',
    value: '',
    labelWidth: '145px',
    label: '原订单号',
    placeholder: '请输入'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    collapseTags: true
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门'
  },
  name: {
    type: 'input',
    value: '',
    label: '用户姓名',
    placeholder: '请输入'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号码',
    placeholder: '请输入'
  },
  order_status: {
    type: 'select',
    label: '退款状态',
    value: '',
    placeholder: '请选择',
    dataList: PAYMENTSTATE
  },
  refund_type: {
    type: 'select',
    label: '退款类型',
    value: '',
    placeholder: '请选择',
    dataList: REFUND_TYPE
  },
  consume_organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  only_rate_fee: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看手续费',
    value: false
  },
  only_debt_fee: {
    type: 'checkbox',
    label: '',
    checkboxLabel: '只看透支',
    value: false
  }
}
// 销售统计筛选设置
export const COLLECT_ORDER_AUTO_SEARCH = {
  pay_time: {
    type: 'daterange',
    label: '支付时间',
    clearable: false,
    value: RECENTONE,
    format: 'yyyy-MM-dd'
  },
  consume_organization_ids: {
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  device_name_list: {
    type: 'select',
    label: '交易设备',
    multiple: true,
    value: [],
    listNameKey: 'device_name',
    listValueKey: 'device_name',
    placeholder: '请选择',
    clearable: true,
    dataList: [],
    collapseTags: true
  },
  meal_type_list: {
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择',
    dataList: MEALTYPE.slice(1)
  },
  food_name: {
    type: 'input',
    value: '',
    label: '商品名称',
    placeholder: '请输入'
  }
}

export const DIC_DATE_TYPE_REUND = [
  {
    label: '提审时间',
    value: 'apply_time'
  },
  {
    label: '处理时间',
    value: 'process_time'
  },
  {
    label: '退款时间',
    value: 'refund_time'
  }
]
// 时间选择字典
export const DIC_DATE_TYPE = [
  {
    label: '提审时间',
    value: 'apply_time'
  },
  {
    label: '处理时间',
    value: 'process_time'
  },
  {
    label: '取消时间',
    value: 'cancel_time'
  }
]

export const DIC_EXAMINE_STATUS = [
  {
    name: '待审批',
    name2: '待处理',
    value: 'applying'
  },
  {
    name: '已同意',
    value: 'agree'
  },
  {
    name: '已拒绝',
    value: 'refuse'
  },
  {
    name: '已撤回',
    value: 'cancel'
  }
]
// 基础信息
export const DIC_BASE_INFO_TABLE_SETTING = [
  { label: '姓名', key: 'name' },
  { label: '人员编号', key: 'person_no' },
  { label: '手机号', key: 'phone' },
  { label: '分组', key: 'payer_group_name' },
  { label: '部门', key: 'payer_department_group_name' }
]

// 充值退款订单筛选
export const RECHARGE_REFUNDE_SEARCH_SETTING = {
  time_type: {
    value: 'apply_time'
  },
  select_time: {
    type: 'datetimerange',
    label: '申请退款时间',
    clearable: true,
    value: getDateRang(-7)
  },
  charge_trade_no: {
    type: 'input',
    value: '',
    label: '充值订单号',
    placeholder: '请输入'
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入'
  },
  charge_out_trade_no: {
    type: 'input',
    value: '',
    label: '第三方订单号',
    placeholder: '请输入'
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '动账组织',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  card_info_department_group_ids: {
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门',
    clearable: true
  },
  card_info_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    clearable: true,
    collapseTags: true
  },
  // pay_scene: {
  //   type: 'select',
  //   label: '充值方式',
  //   value: '',
  //   placeholder: '请选择',
  //   dataList: RECHARGE_TYPEARR
  // },
  payway: {
    type: 'select',
    label: '充值类型',
    value: [],
    placeholder: '请选择',
    multiple: true,
    collapseTags: true,
    dataList: []
  }
}
// 充值退款订单筛选 - 已同意
export const RECHARGE_REFUNDE_SEARCH_SETTING_AGREE = {
  time_type: {
    type: 'select',
    value: 'apply_time',
    maxWidth: '130px',
    dataList: [
      {
        label: '申请时间',
        value: 'apply_time'
      },
      {
        label: '处理时间',
        value: 'process_time'
      },
      {
        label: '退款时间',
        value: 'refund_time'
      }
    ]
  },
  select_time: {
    type: 'datetimerange',
    label: '',
    clearable: true,
    value: getDateRang(-7)
  },
  refund_no: {
    type: 'input',
    value: '',
    label: '充值退款订单号',
    placeholder: '请输入'
  },
  charge_trade_no: {
    type: 'input',
    value: '',
    label: '充值订单号',
    placeholder: '请输入'
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入'
  },
  charge_out_trade_no: {
    type: 'input',
    value: '',
    label: '第三方订单号',
    placeholder: '请输入'
  },
  org_ids: {
    type: 'organizationSelect',
    value: [],
    label: '动账组织',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  card_info_department_group_ids: {
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门',
    clearable: true
  },
  card_info_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    clearable: true,
    collapseTags: true
  },
  refund_type: {
    type: 'select',
    label: '退款方式',
    value: '',
    placeholder: '请选择',
    dataList: REFUNDTYPE
  },
  // pay_scene: {
  //   type: 'select',
  //   label: '退款类型',
  //   value: '',
  //   placeholder: '请选择',
  //   dataList: RECHARGE_TYPEARR
  // },
  payway: {
    type: 'select',
    label: '退款渠道',
    value: '',
    placeholder: '请选择',
    multiple: true,
    dataList: [],
    collapseTags: true
  }
}
// 充值退款订单表头-待处理
export const RECHARGE_REFUNDE_TABLE_SETTING_PENDING = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '充值订单号', key: 'charge_trade_no', width: '160' },
  { label: '第三方订单号', key: 'charge_out_trade_no', width: '160' },
  { label: '申请时间', key: 'apply_time', width: '160' },
  { label: '充值时间', key: 'charge_create_time', width: '160' },
  // { label: '剩余处理时间', key: 'residual_time', width: "160", type: "slot", slotName: "residual" },
  { label: '充值金额', key: 'origin_fee', type: 'money', unit: '¥' },
  { label: '手续费', key: 'rate_fee', type: 'money', unit: '¥' },
  { label: '储值钱包到账', key: 'wallet_fee', type: 'money', unit: '¥' },
  { label: '赠送钱包到账', key: 'complimentary_fee', type: 'money', unit: '¥' },
  { label: '动账组织', key: 'wallet_org' },
  ...DIC_BASE_INFO_TABLE_SETTING,
  { label: '交易流水号', key: 'provider_trade_no' },
  { label: '充值方式', key: 'charge_sub_payway_alias' },
  { label: '充值类型', key: 'charge_payway_alias' },
  { label: '申请原因', key: 'apply_reason', showTooltip: true, width: '160' },
  { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right', width: '100' }
]
// 充值退款订单表头-已同意
export const RECHARGE_REFUNDE_TABLE_SETTING_AGREE = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '原充值订单号', key: 'charge_trade_no', width: '160' },
  { label: '充值退款订单号', key: 'refund_no', width: '160' },
  { label: '申请时间', key: 'apply_time', width: '160' },
  { label: '充值时间', key: 'charge_create_time', width: '160' },
  { label: '处理时间', key: 'process_time', width: '160' },
  { label: '退款时间', key: 'refund_finish_time', width: '160' },
  { label: '退款金额', key: 'refund_fee', width: '160', type: 'money', unit: '¥' },
  { label: '手续费', key: 'rate_fee', type: 'money', unit: '¥' },
  { label: '动账组织', key: 'wallet_org' },
  ...DIC_BASE_INFO_TABLE_SETTING,
  { label: '第三方订单号', key: 'charge_out_trade_no' },
  { label: '交易流水号', key: 'provider_trade_no' },
  { label: '退款类型', key: 'pay_scene_alias' },
  { label: '退款方式', key: 'charge_sub_payway_alias' },
  { label: '退款渠道', key: 'charge_payway_alias' },
  { label: '申请原因', key: 'apply_reason', showTooltip: true, width: '160' },
  { label: '审批备注', key: 'process_reason', showTooltip: true, width: '160' },
  { label: '操作员', key: 'operator_name' }
]
// 充值退款订单表头-已拒绝
export const RECHARGE_REFUNDE_TABLE_SETTING_REFUND = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '充值订单号', key: 'charge_trade_no', width: '160' },
  { label: '申请时间', key: 'apply_time', width: '160' },
  { label: '充值时间', key: 'charge_create_time', width: '160' },
  { label: '充值金额', key: 'origin_fee', width: '160', type: 'money', unit: '¥' },
  { label: '手续费', key: 'rate_fee', type: 'money', unit: '¥' },
  { label: '储值钱包到账', key: 'wallet_fee', type: 'money', unit: '¥' },
  { label: '赠送钱包到账', key: 'complimentary_fee', type: 'money', unit: '¥' },
  { label: '动账组织', key: 'wallet_org' },
  ...DIC_BASE_INFO_TABLE_SETTING,
  { label: '第三方订单号', key: 'charge_out_trade_no' },
  { label: '交易流水号', key: 'provider_trade_no' },
  { label: '支付方式', key: 'charge_sub_payway_alias' },
  { label: '支付类型', key: 'charge_payway_alias' },
  { label: '申请原因', key: 'apply_reason', showTooltip: true, width: '160' },
  { label: '审批备注', key: 'process_reason', showTooltip: true, width: '160' },
  { label: '操作员', key: 'operator_name' }
]

// 充值退款订单表头-已撤回
export const RECHARGE_REFUNDE_TABLE_SETTING_CANCEL = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '充值订单号', key: 'charge_trade_no', width: '160' },
  { label: '第三方订单号', key: 'charge_out_trade_no', width: '160' },
  { label: '申请时间', key: 'apply_time', width: '160' },
  { label: '撤销时间', key: 'process_time', width: '160' },
  { label: '充值时间', key: 'charge_create_time', width: '160' },
  { label: '充值金额', key: 'origin_fee', width: '160', type: 'money', unit: '¥' },
  { label: '手续费', key: 'rate_fee', type: 'money', unit: '¥' },
  { label: '储值钱包到账', key: 'wallet_fee', type: 'money', unit: '¥' },
  { label: '赠送钱包到账', key: 'complimentary_fee', type: 'money', unit: '¥' },
  { label: '动账组织', key: 'wallet_org' },
  ...DIC_BASE_INFO_TABLE_SETTING,
  { label: '交易流水号', key: 'provider_trade_no' },
  { label: '支付方式', key: 'charge_sub_payway_alias' },
  { label: '支付类型', key: 'charge_payway_alias' },
  { label: '申请原因', key: 'apply_reason', showTooltip: true, width: '160' }
]

// 提现申请订单筛选
export const RECHARGE_WITHDRAW_SEARCH_SETTING = {
  time_type: {
    type: 'select',
    value: 'apply_time',
    maxWidth: '130px',
    dataList: [
      {
        label: '提审时间',
        value: 'apply_time'
      }
    ]
  },
  select_time: {
    type: 'datetimerange',
    label: '',
    clearable: false,
    format: 'yyyy-MM-dd HH:mm:ss',
    value: getDateRang(-7)
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '审批单号',
    placeholder: '请输入'
  },
  name: {
    type: 'input',
    value: '',
    label: '姓名',
    placeholder: '请输入'
  },
  phone: {
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入'
  },
  card_info_department_group_ids: {
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门',
    clearable: true
  },
  card_info_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    clearable: true,
    collapseTags: true
  }
}

export const RECHARGE_WITHDRAW_SEARCH_SETTING_ORG = {
  type: 'organizationSelect',
  value: [],
  label: '提现组织',
  checkStrictly: true,
  isLazy: false,
  multiple: true
}

export const RECHARGE_WITHDRAW_SEARCH_SETTING_ORDER = {
  type: 'input',
  value: '',
  label: '充值退款订单号',
  placeholder: '请输入'
}

// 提现申请订单表头-待处理
export const RECHARGE_WITHDRAW_TABLE_SETTING_PENDING = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '审批单号', key: 'trade_no', width: '160' },
  { label: '提审时间', key: 'apply_time', width: '160' },
  { label: '剩余处理时间', key: 'process_time', width: '160', type: 'slot', slotName: 'residual' },
  { label: '申请提现金额', key: 'apply_withdraw_fee', width: '160', type: 'money', unit: '¥' },
  { label: '提现组织', key: 'wallet_org' },
  { label: '提现渠道', key: 'payway_alias' },
  ...DIC_BASE_INFO_TABLE_SETTING,
  { label: '申请原因', key: 'apply_reason', showTooltip: true, width: '160' },
  { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right', width: '100' }
]

// 提现申请订单表头-已同意
export const RECHARGE_WITHDRAW_TABLE_SETTING_AGREEN = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '审批单号', key: 'trade_no', width: '160' },
  { label: '提审时间', key: 'apply_time', width: '160' },
  { label: '处理时间', key: 'process_time', width: '160' },
  { label: '关联订单号', key: 'order_refund_no', width: '160', type: 'slot', slotName: 'reativeTradeNo' },
  { label: '申请提现金额', key: 'apply_withdraw_fee', type: 'money', unit: '¥' },
  { label: '实际提现金额', key: 'real_withdraw_fee', type: 'money', unit: '¥' },
  { label: '提现组织', key: 'wallet_org' },
  // { label: '储值钱包余额', key: 'wallet_balance', type: 'money', unit: '¥' },
  { label: '提现渠道', key: 'payway_alias' },
  ...DIC_BASE_INFO_TABLE_SETTING,
  { label: '申请原因', key: 'apply_reason', showTooltip: true, width: '160' },
  { label: '审批备注', key: 'process_reason', showTooltip: true, width: '160' },
  { label: '操作员', key: 'operator_name' }
]

// 提现申请订单表头-已拒绝
export const RECHARGE_WITHDRAW_TABLE_SETTING_REFUND = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '审批单号', key: 'trade_no', width: '160' },
  { label: '提审时间', key: 'apply_time', width: '160' },
  { label: '处理时间', key: 'process_time' },
  { label: '申请提现金额', key: 'apply_withdraw_fee', type: 'money', unit: '¥' },
  { label: '提现组织', key: 'wallet_org' },
  // { label: '储值钱包余额', key: 'wallet_balance', type: 'money', unit: '¥' },
  { label: '提现渠道', key: 'payway_alias' },
  ...DIC_BASE_INFO_TABLE_SETTING,
  { label: '申请原因', key: 'apply_reason', showTooltip: true, width: '160' },
  { label: '审批备注', key: 'process_reason', showTooltip: true, width: '160' },
  { label: '操作员', key: 'operator_name' }
]

// 提现申请订单表头-已取消
export const RECHARGE_WITHDRAW_TABLE_SETTING_CANCEL = [
  { label: '序号', key: 'index', type: 'index', width: '80' },
  { label: '审批单号', key: 'trade_no', width: '160' },
  { label: '提审时间', key: 'apply_time', width: '160' },
  { label: '取消时间', key: 'process_time', width: '160' },
  { label: '申请提现金额', key: 'apply_withdraw_fee', type: 'money', unit: '¥' },
  { label: '提现组织', key: 'wallet_org' },
  // { label: '储值钱包余额', key: 'wallet_balance', type: 'money', unit: '¥' },
  { label: '提现渠道', key: 'payway_alias' },
  ...DIC_BASE_INFO_TABLE_SETTING,
  { label: '申请原因', key: 'apply_reason', showTooltip: true, width: '160' }
]

export const REFUND_PENDING_ORDER_SEARCH = {
  time_type: {
    type: 'select',
    value: 'create_time',
    maxWidth: '130px',
    dataList: [
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '恢复就餐时间',
        value: 'resume_time'
      },
      {
        label: '退款到账时间',
        value: 'finish_time'
      },
      {
        label: '就餐时间',
        value: 'report_date'
      }
    ]
  },
  select_time: {
    type: 'daterange',
    label: '',
    clearable: false,
    value: RECENTSEVEN
  },
  // trade_no: {
  //   type: 'input',
  //   value: '',
  //   label: '审核编号',
  //   placeholder: '请输入'
  // },
  refund_no: {
    type: 'input',
    value: '',
    label: '退款单号',
    placeholder: '请输入'
  },
  trade_no: {
    type: 'input',
    value: '',
    label: '订单号',
    placeholder: '请输入'
  },
  report_meal_pack_settings_name: {
    type: 'input',
    value: '',
    label: '餐包名称',
    placeholder: '请输入'
  },
  payer_department_group_ids: {
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门'
  },
  payer_group_ids: {
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    collapseTags: true
  },
  name: {
    type: 'input',
    value: '',
    label: '用户姓名',
    placeholder: '请输入'
  },
  person_no: {
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入'
  },
  meal_type_list: {
    type: 'select',
    label: '退费餐段',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择',
    dataList: MEALTYPE.slice(1)
  },
  refund_status_list: {
    type: 'select',
    label: '状态',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择退款状态',
    dataList: [
      { label: '待退款', value: 'ORDER_REFUNDING' },
      { label: '已退款', value: 'ORDER_REFUND_SUCCESS' },
      { label: '已取消', value: 'ORDER_CLOSE' }
    ]
  }
}

// 离线核销订单筛选设置
export const CONSUMPTION_OFFLINE_VERIFICATION_SEARCH_SETTING = {
  date_type: {
    type: 'select',
    value: 'upload_time',
    maxWidth: '100px',
    dataList: [
      {
        label: '上传时间',
        value: 'upload_time'
      },
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '支付时间',
        value: 'pay_time'
      },
      {
        label: '扣款时间',
        value: 'deduction_time'
      }
    ]
  },
  select_time: {
    type: 'daterange',
    label: '',
    clearable: false,
    value: getDateRang(-7, { format: '{y}-{m}-{d}' })
  },
  order_type: {
    labelWidth: '110px',
    type: 'select',
    label: '订单类型',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择',
    dataList: [
      { label: '报餐', value: 'report_meal' },
      { label: '预约-堂食', value: 'instore' },
      { label: '预约-食堂自提', value: 'reservation' }
    ]
  },
  consume_organization_ids: {
    labelWidth: '110px',
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  meal_type_list: {
    labelWidth: '110px',
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择',
    dataList: MEALTYPE.slice(1)
  },

  take_meal_status: {
    labelWidth: '110px',
    type: 'select',
    label: '取餐状态',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择',
    dataList: [
      { value: 'take_out', label: '已取餐' },
      { value: 'no_take', label: '未取餐' },
      { value: 'cancel', label: '已取消' },
      { value: 'time_out', label: '已过期' }
    ]
  },
  order_status: {
    labelWidth: '110px',
    type: 'select',
    label: '支付状态',
    clearable: true,
    value: '',
    placeholder: '请选择',
    dataList: PAYMENTSTATE
  },

  trade_no: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '总单号/订单号/离线单号',
    placeholder: '请输入要搜索总单号/订单号/离线单号'
  },
  payer_group_ids: {
    labelWidth: '110px',
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    collapseTags: true
  },
  payer_department_group_ids: {
    labelWidth: '110px',
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门'
  },

  name: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '用户姓名',
    placeholder: '请输入要搜索的用户姓名'
  },
  phone: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入手机号'
  },
  person_no: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  operator: {
    labelWidth: '110px',
    type: 'input',
    label: '操作人',
    value: '',
    placeholder: '请输入操作人',
    clearable: true
  },
  device_name_list: {
    type: 'select',
    label: '交易设备',
    multiple: true,
    value: [],
    listNameKey: 'device_name',
    listValueKey: 'device_name',
    placeholder: '请选择',
    clearable: true,
    dataList: [],
    collapseTags: true
  }
}
// 离线核销订单表格设置
export const CONSUMPTION_OFFLINE_VERIFICATION_TABLE_SETTING = [
  // { label: '', key: 'selection', type: 'selection', reserveSelection: true },
  { label: '序号', key: 'index', width: '80', type: 'index' },
  { label: '总单号', key: 'order_trade_no', width: '100' },
  { label: '订单号', key: 'trade_no', width: '100' },
  { label: '离线单号', key: 'offline_code', width: '100' },
  { label: '订单类型', key: 'order_type_alias' },
  { label: '上传时间', key: 'create_time', width: '150' },
  { label: '离线核销时间', key: 'take_time', width: '150' },
  { label: '预约时间/报餐时间', key: 'time_range', width: '150', type: 'slot', slotName: 'time_range' },
  { label: '消费点', key: 'org_name', type: 'slot', slotName: 'tooltip' },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '取餐状态', key: 'take_meal_status', type: 'slot', slotName: 'take_meal_status' },
  { label: '支付状态', key: 'order_status_pay', type: 'slot', slotName: 'order_status_pay' },
  { label: '核销设备', key: 'device_name' },
  { label: '用户名', key: 'person_name' },
  { label: '人员编号', key: 'person_no' },
  { label: '手机号', key: 'phone' },
  { label: '分组', key: 'payer_group_name' },
  { label: '部门', key: 'payer_department_group_name' },
  { label: '抓拍人脸', key: 'face_url', type: 'slot', slotName: 'face' },
  { label: '操作人', key: 'operator_name' },
  { label: '操作时间', key: 'update_time', width: '150' },
  { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right', width: '120' }
  // { label: '扣款时间', key: 'deduction_time', width: "150" },
  // { label: '订单金额', key: 'origin_fee', type: 'money' },
  // { label: '附加费用金额', key: 'other_fee', type: 'money' },
  // { label: '优惠金额', key: 'discount_fee', type: 'money' },
  // { label: '补贴消费', key: 'subsidy_fee', type: 'money' },
  // { label: '服务费', key: 'fuwu_fee', type: 'money' },
  // { label: '实收金额', key: 'pay_fee', type: 'money' },
  // { label: '支付状态', key: 'order_status_alias' },
  // { label: '失败原因', key: 'error_reason' },
  // { label: '上传状态', key: 'upload_status_alias' },
  // { label: '设备状态', key: 'pay_device_status_alias' },
  // { label: '补贴动账', key: 'subsidy_fee', type: 'money' },
  // { label: '储值动账', key: 'wallet_fee', type: 'money' },
  // { label: '赠送动账', key: 'complimentary_fee', type: 'money' },
  // { label: '补贴钱包余额', key: 'subsidy_balance', type: 'money' },
  // { label: '储值钱包余额', key: 'wallet_balance', type: 'money' },
  // { label: '赠送钱包余额', key: 'complimentary_balance', type: 'money' },
]

// 消费订单表头-记账
export const CONSUMPTION_ACCOUNTING_TABLE_SETTING = [
  { label: '', key: 'selection', type: 'selection', reserveSelection: true },
  { label: '序号', key: 'index', width: '80', type: 'slot', slotName: 'index' },
  { label: '总单号', key: 'unified_out_trade_no', width: '150' },
  { label: '订单号', key: 'trade_no', width: '150' },
  { label: '上传时间', key: 'create_time_upload', width: '150', type: "slot", slotName: "create_time_upload" },
  { label: '创建时间', key: 'create_time', width: '150' },
  { label: '支付时间', key: 'pay_time', width: '150' },
  { label: '扣款时间', key: 'deduction_time', width: '150' },
  { label: '订单金额', key: 'origin_fee', type: 'money' },
  { label: '支付方式', key: 'sub_payway_alias' },
  { label: '扣款状态', key: 'order_status_alias' },
  { label: '失败原因', key: 'error_reason' },
  { label: '餐段', key: 'meal_type_alias' },
  { label: '消费设备', key: 'device_name' },
  { label: '用户名', key: 'name' },
  { label: '人员编号', key: 'person_no' },
  { label: '手机号', key: 'phone' },
  { label: '抓拍人脸', key: 'face_url', type: 'slot', slotName: 'face' },
  { label: '分组', key: 'payer_group_name' },
  { label: '部门', key: 'payer_department_group_name' },
  { label: '操作人', key: 'operator' },
  { label: '操作时间', key: 'update_time', width: '150' },
  { label: '操作', key: 'operation', type: 'slot', slotName: 'operation', fixed: 'right', width: '180' }
]

export const CONSUMPTION_ACCOUNTING_SEARCH_SETTING = {
  date_type: {
    type: 'select',
    value: 'upload_time',
    maxWidth: '100px',
    dataList: [
      {
        label: '上传时间',
        value: 'upload_time'
      },
      {
        label: '创建时间',
        value: 'create_time'
      },
      {
        label: '支付时间',
        value: 'pay_time'
      },
      {
        label: '扣款时间',
        value: 'deduction_time'
      }
    ]
  },
  select_time: {
    type: 'daterange',
    label: '',
    clearable: false,
    value: getDateRang(-7, { format: '{y}-{m}-{d}' })
  },
  consume_organization_ids: {
    labelWidth: '110px',
    type: 'organizationSelect',
    value: [],
    label: '消费点',
    checkStrictly: true,
    isLazy: false,
    multiple: true
  },
  meal_type_list: {
    labelWidth: '110px',
    type: 'select',
    label: '餐段',
    value: [],
    multiple: true,
    collapseTags: true,
    placeholder: '请选择',
    dataList: MEALTYPE.slice(1)
  },
  order_status_multi: {
    labelWidth: '110px',
    type: 'select',
    label: '扣款状态',
    multiple: true,
    collapseTags: true,
    placeholder: '请选择',
    filterable: true,
    clearable: true,
    value: [],
    dataList: [
      {
        label: '待支付',
        value: 'ORDER_PAYING,ORDER_FAILED'
      },
      {
        label: '成功',
        value: 'ORDER_SUCCESS'
      },
      {
        label: '失败',
        value: 'ORDER_FAILED'
      }
      // {
      //   label: '取消',
      //   value: 'ORDER_CLOSE'
      // }
    ]
  },
  trade_no: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '总单号/订单号',
    placeholder: '请输入要搜索的订单号'
  },
  sub_payway_list: {
    type: 'select',
    value: '',
    label: '支付方式',
    multiple: true,
    collapseTags: true,
    dataList: [],
    clearable: true
  },
  device_name_list: {
    labelWidth: '110px',
    type: 'select',
    label: '设备',
    value: '',
    multiple: true,
    collapseTags: true,
    filterable: true,
    listNameKey: 'device_name',
    listValueKey: 'device_name',
    placeholder: '请选择',
    clearable: true,
    dataList: []
  },
  payer_group_ids: {
    labelWidth: '110px',
    type: 'groupSelect',
    label: '分组',
    value: [],
    placeholder: '请选择分组',
    multiple: true,
    collapseTags: true
  },
  payer_department_group_ids: {
    labelWidth: '110px',
    type: 'organizationDepartmentSelect',
    value: [],
    label: '部门'
  },
  name: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '用户姓名',
    placeholder: '请输入要搜索的用户姓名'
  },
  phone: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '手机号',
    placeholder: '请输入手机号'
  },
  person_no: {
    labelWidth: '110px',
    type: 'input',
    value: '',
    label: '人员编号',
    placeholder: '请输入人员编号'
  },
  operator: {
    labelWidth: '110px',
    type: 'input',
    label: '操作人',
    value: '',
    placeholder: '请输入操作人',
    clearable: true
  }
}
